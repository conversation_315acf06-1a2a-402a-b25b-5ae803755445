import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export async function DELETE() {
  try {
    const prisma = new PrismaClient();

    // Usuń numery telefonów powiązane z ogłoszeniami
    const phoneResult = await prisma.phone.deleteMany({});

    // Usuń numery telefonów do wysyłki
    const phoneToSendResult = await prisma.phoneToSend.deleteMany({});

    await prisma.$disconnect();

    const totalCount = phoneResult.count + phoneToSendResult.count;

    return NextResponse.json({
      success: true,
      count: totalCount,
      phoneCount: phoneResult.count,
      phoneToSendCount: phoneToSendResult.count,
      message: `Usunięto wszystkie numery telefonów (${phoneResult.count} z ogłoszeń + ${phoneToSendResult.count} do wysyłki = ${totalCount} łącznie)`
    });
  } catch (error) {
    console.error('Błąd podczas usuwania numerów telefonów:', error);
    return NextResponse.json(
      { success: false, error: 'Wystąpił błąd podczas usuwania numerów telefonów' },
      { status: 500 }
    );
  }
}