import axios from 'axios';
import * as cheerio from 'cheerio';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

interface QueueOptions {
  maxConcurrent: number;
  retryLimit: number;
  retryDelay: number;
  randomDelay: boolean;
  minDelayMs: number;
  maxDelayMs: number;
  userAgents: string[];
}

type QueueTask<T> = {
  execute: () => Promise<T>;
  resolve: (value: T) => void;
  reject: (reason: unknown) => void;
  retries: number;
};

// Singleton dla zatrzymywania procesu Cheerio
export class CheerioController {
  private static instance: CheerioController;
  private _shouldStop: boolean = false;

  private constructor() {}

  public static getInstance(): CheerioController {
    if (!CheerioController.instance) {
      CheerioController.instance = new CheerioController();
    }
    return CheerioController.instance;
  }

  get shouldStop(): boolean {
    return this._shouldStop;
  }

  public stopProcessing(): void {
    this._shouldStop = true;
    console.log('[CheerioController] Zatrzymanie procesu Cheerio zostało zasygnalizowane');
  }

  public resetStop(): void {
    this._shouldStop = false;
    console.log('[CheerioController] Sygnał zatrzymania Cheerio został zresetowany');
  }
}

export class CheerioHelper {
  // Fallback liczba stron do scrapowania gdy nie można określić rzeczywistej liczby
  private static readonly FALLBACK_PAGES_COUNT = 4;

  // Rate limiting dla Gemini API
  private geminiRequestQueue: Array<{ locationText: string; resolve: (value: string | null) => void; reject: (error: Error) => void }> = [];
  private geminiRequestCount = 0;
  private geminiLastMinute = 0;
  private geminiProcessing = false;

  // Cache dla Gemini odpowiedzi
  private geminiCache: Map<string, string | null> = new Map();

  private userAgents: string[] = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  ];

  // Lista dozwolonych województw
  private allowedRegions: string[] = [
    'Mazowieckie', 'Łódzkie', 'Lubelskie', 'Świętokrzyskie', 'Podlaskie'
  ];

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private queue: QueueTask<any>[] = [];
  private activeCount: number = 0;
  private options: QueueOptions;
  private logPath: string;
  
  // Licznik requestów do monitorowania limitów
  private requestCounter: number = 0;
  private readonly REQUEST_LIMIT: number = 500;
  private readonly PAUSE_DURATION_MS: number = 30000; // 30 sekund
  private isPaused: boolean = false;

  constructor(options?: Partial<QueueOptions>) {
    this.options = {
      maxConcurrent: 3,
      retryLimit: 3,
      retryDelay: 1000,
      randomDelay: true,
      minDelayMs: 100,
      maxDelayMs: 500,
      userAgents: this.userAgents,
      ...options
    };

    // Ustawienie ścieżki do logów
    this.logPath = path.join(process.cwd(), 'logs', 'cheerio.log');
    const logDir = path.dirname(this.logPath);
    
    // Upewnij się, że katalog istnieje
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private getRandomUserAgent(): string {
    return this.options.userAgents[Math.floor(Math.random() * this.options.userAgents.length)];
  }

  private getRandomDelay(): number {
    if (this.options.randomDelay) {
      return Math.floor(
        Math.random() * (this.options.maxDelayMs - this.options.minDelayMs) + this.options.minDelayMs
      );
    }
    return this.options.minDelayMs;
  }

  // Funkcja do przetwarzania kolejki Gemini z rate limiting
  private async processGeminiQueue(): Promise<void> {
    if (this.geminiProcessing || this.geminiRequestQueue.length === 0) {
      return;
    }

    this.geminiProcessing = true;

    while (this.geminiRequestQueue.length > 0) {
      const currentMinute = Math.floor(Date.now() / 60000);

      // Reset licznika co minutę
      if (currentMinute !== this.geminiLastMinute) {
        this.geminiRequestCount = 0;
        this.geminiLastMinute = currentMinute;
      }

      // Sprawdź limit (14 żądań na minutę, zostawiamy 1 jako bufor)
      if (this.geminiRequestCount >= 14) {
        await this.logAction('info', `Osiągnięto limit Gemini API (14/15 żądań na minutę). Oczekiwanie...`);
        // Czekaj do następnej minuty
        const waitTime = (currentMinute + 1) * 60000 - Date.now();
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }

      const request = this.geminiRequestQueue.shift();
      if (!request) break;

      try {
        this.geminiRequestCount++;
        await this.logAction('info', `Gemini API żądanie ${this.geminiRequestCount}/14 w tej minucie dla: "${request.locationText}"`);

        const result = await this.callGeminiAPI(request.locationText);
        request.resolve(result);

        // Dodaj małe opóźnienie między żądaniami (4 sekundy = 15 żądań na minutę)
        await new Promise(resolve => setTimeout(resolve, 4100));

      } catch (error) {
        request.reject(error instanceof Error ? error : new Error(String(error)));
      }
    }

    this.geminiProcessing = false;
  }

  // Funkcja do bezpośredniego wywołania Gemini API
  private async callGeminiAPI(locationText: string): Promise<string | null> {
    try {
      const apiKey = 'AIzaSyANM77YO9DT5yKFZb6ChKcZJQWURLx-25k';
      const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

      const prompt = `${locationText}\nJakie to województwo? Odpowiedz jednym słowem, np. podlaskie, mazowieckie, łódzkie, itd.`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ]
        })
      });

      if (!response.ok) {
        await this.logAction('error', `Błąd API Gemini: ${response.status} ${response.statusText}`);
        return null;
      }

      const data = await response.json();
      const geminiResponse = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim().toLowerCase();

      if (geminiResponse) {
        await this.logAction('info', `Gemini rozpoznał województwo dla "${locationText}": "${geminiResponse}"`);
        return geminiResponse;
      }

      return null;
    } catch (error) {
      await this.logAction('error', `Błąd podczas wywołania Gemini API: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  // Funkcja do rozpoznawania województwa za pomocą Gemini 2.0 Flash z rate limiting
  private async getRegionFromGemini(locationText: string): Promise<string | null> {
    // Sprawdź cache
    const normalizedText = locationText.toLowerCase().trim();
    if (this.geminiCache.has(normalizedText)) {
      const cachedResult = this.geminiCache.get(normalizedText) || null;
      await this.logAction('info', `Gemini cache hit dla "${locationText}": "${cachedResult}"`);
      return cachedResult;
    }

    return new Promise((resolve, reject) => {
      // Dodaj żądanie do kolejki
      this.geminiRequestQueue.push({
        locationText,
        resolve: (result) => {
          // Zapisz w cache
          this.geminiCache.set(normalizedText, result);
          resolve(result);
        },
        reject
      });

      // Rozpocznij przetwarzanie kolejki
      this.processGeminiQueue().catch(reject);
    });
  }

  private async logAction(level: 'info' | 'warn' | 'error', message: string): Promise<void> {
    try {
      // Format logu
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        level,
        message: `[Cheerio] ${message}`
      };
      
      // Zapisz log do konsoli
      console[level](`[${timestamp}] [Cheerio] ${message}`);
      
      // Zapisz log do pliku
      fs.appendFileSync(
        this.logPath,
        JSON.stringify(logEntry) + os.EOL,
        'utf-8'
      );
    } catch (error) {
      console.error('Błąd podczas logowania:', error);
    }
  }
  
  // Metoda do sprawdzania i obsługi limitu requestów
  private async checkRequestLimit(): Promise<void> {
    // Inkrementuj licznik requestów
    this.requestCounter++;
    
    // Sprawdź czy przekroczono limit
    if (this.requestCounter >= this.REQUEST_LIMIT) {
      // Jeśli przekroczono limit, zrób pauzę
      this.isPaused = true;
      
      await this.logAction('warn', `Osiągnięto limit ${this.REQUEST_LIMIT} requestów. Pauza ${this.PAUSE_DURATION_MS/1000} sekund...`);
      
      // Pauza na określony czas
      await new Promise(resolve => setTimeout(resolve, this.PAUSE_DURATION_MS));
      
      // Po pauzie zresetuj licznik
      this.requestCounter = 0;
      this.isPaused = false;
      
      await this.logAction('info', `Zakończono pauzę po wykonaniu ${this.REQUEST_LIMIT} requestów. Licznik zresetowany.`);
    } else if (this.requestCounter % 10 === 0) {
      // Co 10 requestów informuj o aktualnej liczbie
      await this.logAction('info', `Wykonano ${this.requestCounter}/${this.REQUEST_LIMIT} requestów przed pauzą`);
    }
  }

  async fetchWithCheerio(url: string): Promise<cheerio.CheerioAPI> {
    // Sprawdź limit requestów przed wykonaniem zapytania
    await this.checkRequestLimit();
    
    const delay = this.getRandomDelay();
    await this.logAction('info', `Pobieranie ${url} z opóźnieniem ${delay}ms`);
    
    // Wprowadź losowe opóźnienie
    await new Promise(resolve => setTimeout(resolve, delay));
    
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': this.getRandomUserAgent(),
          'Accept': 'text/html,application/xhtml+xml,application/xml',
          'Accept-Language': 'pl,en-US;q=0.7,en;q=0.3',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Referer': 'https://www.otomoto.pl/',
          'DNT': '1',
        },
        timeout: 15000,
      });
      
      return cheerio.load(response.data);
    } catch (error) {
      // Jeśli otrzymamy błąd 403, zróbmy dłuższą pauzę (symuluję mechanizm limitu)
      if (axios.isAxiosError(error) && error.response?.status === 403) {
        this.isPaused = true;
        await this.logAction('warn', `Otrzymano błąd 403 - prawdopodobnie osiągnięto limit zapytań. Pauza ${this.PAUSE_DURATION_MS/1000} sekund...`);
        await new Promise(resolve => setTimeout(resolve, this.PAUSE_DURATION_MS));
        this.isPaused = false;
        this.requestCounter = 0; // Resetujemy licznik po pauzie
        await this.logAction('info', `Zakończono pauzę po błędzie 403. Licznik zresetowany.`);
      }
      throw error;
    }
  }

  async fetchJson<T>(url: string): Promise<T> {
    // Sprawdź limit requestów przed wykonaniem zapytania
    await this.checkRequestLimit();
    
    const delay = this.getRandomDelay();
    await this.logAction('info', `Pobieranie JSON ${url} z opóźnieniem ${delay}ms`);
    
    // Wprowadź losowe opóźnienie
    await new Promise(resolve => setTimeout(resolve, delay));
    
    try {
      const response = await axios.get<T>(url, {
        headers: {
          'User-Agent': this.getRandomUserAgent(),
          'Accept': 'application/json',
          'Accept-Language': 'pl,en-US;q=0.7,en;q=0.3',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Referer': 'https://www.otomoto.pl/',
          'DNT': '1',
        },
        timeout: 15000,
      });
      
      return response.data;
    } catch (error) {
      // Jeśli otrzymamy błąd 403, zróbmy dłuższą pauzę
      if (axios.isAxiosError(error) && error.response?.status === 403) {
        this.isPaused = true;
        await this.logAction('warn', `Otrzymano błąd 403 - prawdopodobnie osiągnięto limit zapytań. Pauza ${this.PAUSE_DURATION_MS/1000} sekund...`);
        await new Promise(resolve => setTimeout(resolve, this.PAUSE_DURATION_MS));
        this.isPaused = false;
        this.requestCounter = 0; // Resetujemy licznik po pauzie
        await this.logAction('info', `Zakończono pauzę po błędzie 403. Licznik zresetowany.`);
      }
      throw error;
    }
  }

  // Dodaje zadanie do kolejki z obsługą promisów
  enqueue<T>(task: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.queue.push({
        execute: task,
        resolve,
        reject,
        retries: 0
      });
      
      this.processQueue();
    });
  }

  // Przetwarza kolejkę zadań z limitem jednoczesnych wykonań
  private async processQueue(): Promise<void> {
    // Sprawdź, czy mamy zatrzymać przetwarzanie
    if (CheerioController.getInstance().shouldStop) {
      // Opróżnij kolejkę i odrzuć wszystkie zadania z informacją o zatrzymaniu
      while (this.queue.length > 0) {
        const task = this.queue.shift()!;
        task.reject(new Error('Zatrzymano przetwarzanie Cheerio'));
      }
      return;
    }
    
    // Jeśli scraper jest w trybie pauzy, nie przetwarzaj kolejnych zadań
    if (this.isPaused) {
      // Zaplanuj ponowne sprawdzenie po krótkim czasie
      setTimeout(() => this.processQueue(), 1000);
      return;
    }
    
    if (this.activeCount >= this.options.maxConcurrent || this.queue.length === 0) {
      return;
    }

    this.activeCount++;
    const task = this.queue.shift()!;

    try {
      // Sprawdź przed wykonaniem zadania
      if (CheerioController.getInstance().shouldStop) {
        task.reject(new Error('Zatrzymano przetwarzanie Cheerio'));
        return;
      }
      
      // Sprawdź ponownie, czy nie weszliśmy w tryb pauzy
      if (this.isPaused) {
        // Dodaj zadanie z powrotem do kolejki i przerwij
        this.queue.unshift(task);
        this.activeCount--;
        // Zaplanuj ponowne sprawdzenie
        setTimeout(() => this.processQueue(), 1000);
        return;
      }
      
      const result = await task.execute();
      task.resolve(result);
    } catch (error) {
      // Sprawdź przed ponawianiem
      if (CheerioController.getInstance().shouldStop) {
        task.reject(new Error('Zatrzymano przetwarzanie Cheerio'));
      } else if (task.retries < this.options.retryLimit) {
        task.retries++;
        const delayMs = this.options.retryDelay * Math.pow(2, task.retries - 1);
        
        await this.logAction('warn', `Ponowna próba (${task.retries}/${this.options.retryLimit}) po ${delayMs}ms`);
        
        await new Promise(resolve => setTimeout(resolve, delayMs));
        this.queue.push(task);
      } else {
        task.reject(error);
      }
    } finally {
      this.activeCount--;
      // Kontynuuj przetwarzanie kolejki, o ile nie mamy się zatrzymać i nie jesteśmy w pauzie
      if (!CheerioController.getInstance().shouldStop && !this.isPaused) {
        this.processQueue();
      } else if (this.isPaused) {
        // Jeśli jesteśmy w pauzie, zaplanuj sprawdzenie kolejki po zakończeniu pauzy
        setTimeout(() => this.processQueue(), 1000);
      }
    }
  }

  // Pobieranie wszystkich ID ogłoszeń z podanej strony
  async scrapeAdIds(url: string): Promise<string[]> {
    try {
      // Sprawdź, czy mamy zatrzymać przetwarzanie
      if (CheerioController.getInstance().shouldStop) {
        await this.logAction('info', `Scrapowanie ID ogłoszeń przerwane przez flag shouldStop`);
        return [];
      }

      const $ = await this.fetchWithCheerio(url);
      const adIds: string[] = [];

      // DEBUG: Sprawdź co znajdujemy na stronie
      await this.logAction('info', `DEBUG: Sprawdzanie struktury strony dla ${url}`);

      // Sprawdź różne selektory
      const searchResults = $('[data-testid="search-results"]');
      await this.logAction('info', `DEBUG: Znaleziono ${searchResults.length} elementów z data-testid="search-results"`);

      if (searchResults.length > 0) {
        const directDivs = searchResults.children('div');
        await this.logAction('info', `DEBUG: Znaleziono ${directDivs.length} bezpośrednich div-ów w search-results`);

        const articles = searchResults.find('article');
        await this.logAction('info', `DEBUG: Znaleziono ${articles.length} article-ów w search-results`);

        const articlesWithDataId = searchResults.find('article[data-id]');
        await this.logAction('info', `DEBUG: Znaleziono ${articlesWithDataId.length} article-ów z data-id w search-results`);
      }

      // Sprawdź alternatywne selektory
      const allArticles = $('article');
      await this.logAction('info', `DEBUG: Znaleziono ${allArticles.length} wszystkich article-ów na stronie`);

      const allDataIds = $('[data-id]');
      await this.logAction('info', `DEBUG: Znaleziono ${allDataIds.length} wszystkich elementów z data-id na stronie`);

      // Selektor dla kontenera ogłoszeń - taki sam jak w puppeteer-scraper.ts
      const adElements = $('[data-testid="search-results"] > div > article');
      await this.logAction('info', `DEBUG: Główny selektor znalazł ${adElements.length} elementów`);

      // Zawsze spróbuj najprostszego selektora jako głównego
      const simpleArticles = $('article[data-id]');
      await this.logAction('info', `DEBUG: Prosty selektor article[data-id] znalazł ${simpleArticles.length} elementów`);

      // Jeśli główny selektor nie działa, spróbuj alternatywnych
      let elementsToProcess = adElements;
      if (adElements.length === 0) {
        await this.logAction('warn', `DEBUG: Główny selektor nie znalazł elementów, próbuję alternatywnych`);

        // Alternatywny selektor 1: wszystkie article z data-id (najprostszy)
        if (simpleArticles.length > 0) {
          await this.logAction('info', `DEBUG: Używam prostego selektora article[data-id] - znalazł ${simpleArticles.length} elementów`);
          elementsToProcess = simpleArticles;
        } else {
          // Alternatywny selektor 2: sprawdź czy struktura się zmieniła
          const alt2 = $('[data-testid="search-results"] article');
          if (alt2.length > 0) {
            await this.logAction('info', `DEBUG: Alternatywny selektor 2 znalazł ${alt2.length} elementów`);
            elementsToProcess = alt2;
          } else {
            await this.logAction('warn', `DEBUG: Żaden selektor nie znalazł elementów ogłoszeń`);
          }
        }
      }

      // Zbierz informacje o elementach do debugowania
      const debugInfo: string[] = [];

      elementsToProcess.each((_, element) => {
        // Sprawdź, czy mamy zatrzymać przetwarzanie w trakcie pętli
        if (CheerioController.getInstance().shouldStop) {
          return false; // Przerwij pętlę each
        }

        const adId = $(element).attr('data-id');
        debugInfo.push(`Element ${_}: data-id="${adId}"`);

        if (adId && adId !== 'BRAK') {
          adIds.push(adId);
        }
      });

      // Wyloguj informacje debug
      for (const info of debugInfo) {
        await this.logAction('info', `DEBUG: ${info}`);
      }

      if (CheerioController.getInstance().shouldStop) {
        await this.logAction('info', `Scrapowanie ID ogłoszeń przerwane przez flagę shouldStop - pobrano ${adIds.length} ID ogłoszeń`);
      } else {
        await this.logAction('info', `Zescrapowano ${adIds.length} ID ogłoszeń z ${url}`);
      }

      return adIds;
    } catch (error) {
      await this.logAction('error', `Błąd podczas scrapowania ID ogłoszeń: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  // Funkcja sprawdzająca czy województwo jest dozwolone
  private isAllowedRegion(location: string | undefined, directRegion?: string): { allowed: boolean; region: string | null } {
    // Jeśli mamy bezpośrednio podany region (z JSON), użyj go
    if (directRegion) {
      return {
        allowed: this.allowedRegions.includes(directRegion),
        region: directRegion
      };
    }
    
    // Stara metoda jako fallback, jeśli nie mamy bezpośredniego regionu
    if (!location) return { allowed: false, region: null };
    
    // Próbujemy wyciągnąć województwo - format "Miasto (Województwo)"
    const regionMatch = location.match(/\(([^)]+)\)$/);
    const region = regionMatch ? regionMatch[1].trim() : null;
    
    // Jeśli nie znaleziono województwa, zakładamy że nie jest dozwolone
    if (!region) return { allowed: false, region: null };
    
    // Sprawdź czy województwo jest na liście dozwolonych
    return { 
      allowed: this.allowedRegions.includes(region),
      region 
    };
  }

  // Funkcja do wyciągania lokalizacji z treści ogłoszenia
  private async extractLocationFromAdHtml(html: string, adId: string): Promise<string | null> {
    try {
      const $ = cheerio.load(html);

      await this.logAction('info', `DEBUG: Rozpoczynam wyciąganie lokalizacji dla ogłoszenia ${adId}`);

      // Metoda 1: Szukanie lokalizacji w skrypcie __NEXT_DATA__ za pomocą wyrażeń regularnych
      const nextDataScript = $('#__NEXT_DATA__');
      if (nextDataScript.length > 0) {
        const scriptContent = nextDataScript.html();
        if (scriptContent) {
          await this.logAction('info', `DEBUG: Szukam danych lokalizacji w skrypcie __NEXT_DATA__ za pomocą wyrażeń regularnych`);
          
          // Szukamy danych lokalizacji za pomocą kilku różnych wyrażeń regularnych
          // 1. Pełna lokalizacja: "location":{"address":"XXX","city":"XXX","region":"XXX"}
          const regexFull = /"location":\s*{\s*"address":\s*"([^"]+)",\s*"city":\s*"([^"]+)",\s*"region":\s*"([^"]+)"/i;
          const matchFull = scriptContent.match(regexFull);

          if (matchFull) {
            const address = matchFull[1];
            const city = matchFull[2];
            const region = matchFull[3];

            await this.logAction('info', `DEBUG: Znaleziono dane lokalizacji wyrażeniem regularnym - address: "${address}", city: "${city}", region: "${region}"`);

            // Sformatuj lokalizację
            const formattedLocation = `${address} (${region})`;
            await this.logAction('info', `Pomyślnie wyciągnięto lokalizację z wyrażenia regularnego dla ogłoszenia ${adId}: "${formattedLocation}"`);
            return formattedLocation;
          }

          // 2. Alternatywne wzorce dla różnych kolejności pól
          const regexAlt1 = /"location":\s*{\s*"city":\s*"([^"]+)",\s*"address":\s*"([^"]+)",\s*"region":\s*"([^"]+)"/i;
          const regexAlt2 = /"location":\s*{[^}]*"region":\s*"([^"]+)"[^}]*"address":\s*"([^"]+)"/i;
          const regexAlt3 = /"location":\s*{[^}]*"region":\s*"([^"]+)"[^}]*"city":\s*"([^"]+)"/i;

          const matchAlt1 = scriptContent.match(regexAlt1);
          const matchAlt2 = scriptContent.match(regexAlt2);
          const matchAlt3 = scriptContent.match(regexAlt3);

          if (matchAlt1) {
            const address = matchAlt1[2];
            const region = matchAlt1[3];
            const formattedLocation = `${address} (${region})`;
            await this.logAction('info', `Pomyślnie wyciągnięto lokalizację z alternatywnego wzorca 1 dla ogłoszenia ${adId}: "${formattedLocation}"`);
            return formattedLocation;
          } else if (matchAlt2) {
            const region = matchAlt2[1];
            const address = matchAlt2[2];
            const formattedLocation = `${address} (${region})`;
            await this.logAction('info', `Pomyślnie wyciągnięto lokalizację z alternatywnego wzorca 2 dla ogłoszenia ${adId}: "${formattedLocation}"`);
            return formattedLocation;
          } else if (matchAlt3) {
            const region = matchAlt3[1];
            const city = matchAlt3[2];
            const formattedLocation = `${city} (${region})`;
            await this.logAction('info', `Pomyślnie wyciągnięto lokalizację z alternatywnego wzorca 3 dla ogłoszenia ${adId}: "${formattedLocation}"`);
            return formattedLocation;
          }

          // 3. Szukamy osobno adresu i regionu, które mogą być w różnych miejscach
          const addressRegex = /"address":\s*"([^"]+)"/i;
          const cityRegex = /"city":\s*"([^"]+)"/i;
          const regionRegex = /"region":\s*"([^"]+)"/i;

          const addressMatch = scriptContent.match(addressRegex);
          const cityMatch = scriptContent.match(cityRegex);
          const regionMatch = scriptContent.match(regionRegex);

          if (regionMatch && (addressMatch || cityMatch)) {
            const address = addressMatch ? addressMatch[1] : (cityMatch ? cityMatch[1] : null);
            const region = regionMatch[1];

            if (address && region) {
              const formattedLocation = `${address} (${region})`;
              await this.logAction('info', `Pomyślnie wyciągnięto lokalizację z osobnych wyrażeń regularnych dla ogłoszenia ${adId}: "${formattedLocation}"`);
              return formattedLocation;
            }
          } else if (addressMatch || cityMatch) {
            // Jeśli znaleźliśmy tylko adres/miasto bez województwa, użyj Gemini API
            const locationText = addressMatch ? addressMatch[1] : cityMatch![1];
            await this.logAction('info', `DEBUG: Znaleziono lokalizację bez województwa w regex: "${locationText}", używam Gemini API`);

            const geminiRegion = await this.getRegionFromGemini(locationText);
            if (geminiRegion) {
              const formattedLocation = `${locationText} (${geminiRegion})`;
              await this.logAction('info', `Pomyślnie wyciągnięto lokalizację z regex + Gemini dla ogłoszenia ${adId}: "${formattedLocation}"`);
              return formattedLocation;
            }
          }
        }
      }

      // Metoda 2: Szukanie bezpośrednio w treści strony
      await this.logAction('info', `DEBUG: Szukam lokalizacji bezpośrednio w treści HTML`);
      
      // Lista selektorów do przeszukania
      const locationSelectors = [
        '[data-testid="ad-location"]',
        '[data-parameter="location"]',
        '[class*="location"]',
        '[class*="address"]',
        '[id*="location"]',
        'span:contains("Lokalizacja:")',
        'div:contains("Lokalizacja:")'
      ];
      
      for (const selector of locationSelectors) {
        const elements = $(selector);
        if (elements.length > 0) {
          await this.logAction('info', `DEBUG: Znaleziono ${elements.length} elementów dla selektora "${selector}"`);
          
          for (let i = 0; i < elements.length; i++) {
            const element = elements.eq(i);
            const locationText = element.text().trim();
            
            if (locationText && locationText.length > 5) {
              await this.logAction('info', `DEBUG: Potencjalna lokalizacja z selektora "${selector}": "${locationText}"`);
              
              // Próbujemy wyekstrahować województwo z tekstu
              let region: string | null = null;
              let address: string | null = null;
              
              // Format "Miasto (Województwo)"
              const bracketMatch = locationText.match(/^(.*?)\s*\(\s*([^)]+)\s*\)$/);
              if (bracketMatch) {
                address = bracketMatch[1].trim();
                region = bracketMatch[2].trim();
              } else {
                // Format "Miasto, województwo"
                const commaMatch = locationText.match(/^(.*?),\s*([^,]+)$/);
                if (commaMatch) {
                  address = commaMatch[1].trim();
                  region = commaMatch[2].trim();
                } else {
                  address = locationText;
                }
              }
              
              if (region && this.allowedRegions.includes(region)) {
                const formattedLocation = `${address} (${region})`;
                await this.logAction('info', `Pomyślnie wyciągnięto lokalizację z HTML dla ogłoszenia ${adId}: "${formattedLocation}"`);
                return formattedLocation;
              } else if (address) {
                // Jeśli znaleźliśmy tylko adres, szukamy województwa w innych miejscach w HTML
                for (const region of this.allowedRegions) {
                  if (html.includes(region)) {
                    const formattedLocation = `${address} (${region})`;
                    await this.logAction('info', `Znaleziono lokalizację z adresem "${address}" i województwem "${region}" dla ogłoszenia ${adId}`);
                    return formattedLocation;
                  }
                }
                
                // Zwracamy sam adres jako ostateczność
                await this.logAction('info', `Zwracam sam adres (bez województwa) dla ogłoszenia ${adId}: "${address}"`);
                return address;
              }
            }
          }
        }
      }

      await this.logAction('warn', `Nie udało się znaleźć lokalizacji dla ogłoszenia ${adId} używając żadnej metody`);
      return null;
    } catch (error) {
      await this.logAction('error', `Błąd podczas wyciągania lokalizacji dla ogłoszenia ${adId}: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  // Pobranie szczegółów ogłoszenia z API wraz z lokalizacją i filtrowaniem województw
  async fetchAdDetails(adId: string): Promise<Record<string, unknown> | { ignoredByRegion: boolean; location: string }> {
    try {
      // Najpierw przejdź do strony ogłoszenia, aby wyciągnąć lokalizację
      const adUrl = `https://www.otomoto.pl/osobowe/${adId}`;
      await this.logAction('info', `Pobieranie strony ogłoszenia: ${adUrl}`);
      
      try {
        // Pobierz HTML strony ogłoszenia
        const response = await axios.get(adUrl, {
          headers: {
            'User-Agent': this.getRandomUserAgent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml',
            'Accept-Language': 'pl,en-US;q=0.7,en;q=0.3',
          },
          timeout: 15000,
        });
        
        // Wyciągnij dane lokalizacji i region z JSON w __NEXT_DATA__
        const $ = cheerio.load(response.data);
        const nextDataScript = $('#__NEXT_DATA__');
        
        // Dodaj logowanie informacji o znalezieniu skryptu
        await this.logAction('info', `DEBUG: Znaleziono ${nextDataScript.length} elementów script#__NEXT_DATA__ dla ogłoszenia ${adId}`);
        
        if (nextDataScript.length > 0) {
          const scriptContent = nextDataScript.html();
          
          // Dodaj logowanie o długości zawartości skryptu
          if (scriptContent) {
            await this.logAction('info', `DEBUG: Długość zawartości script#__NEXT_DATA__: ${scriptContent.length} znaków`);
            
            // Dodaj logowanie kluczy najwyższego poziomu w JSON
            try {
              const topLevelPreview = JSON.parse(scriptContent);
              const topKeys = Object.keys(topLevelPreview || {});
              await this.logAction('info', `DEBUG: Klucze najwyższego poziomu w JSON: ${topKeys.join(', ')}`);
              
              // Pokaż klucze props jeśli istnieje
              if (topLevelPreview?.props) {
                const propsKeys = Object.keys(topLevelPreview.props);
                await this.logAction('info', `DEBUG: Klucze w props: ${propsKeys.join(', ')}`);
                
                // Pokaż klucze pageProps jeśli istnieje
                if (topLevelPreview.props.pageProps) {
                  const pagePropsKeys = Object.keys(topLevelPreview.props.pageProps);
                  await this.logAction('info', `DEBUG: Klucze w pageProps: ${pagePropsKeys.join(', ')}`);
                  
                  // Sprawdź urqlState, który może zawierać dane lokalizacji
                  if (topLevelPreview.props.pageProps.urqlState) {
                    await this.logAction('info', `DEBUG: Znaleziono urqlState, sprawdzam zawartość...`);
                    // Przeszukaj urqlState - to może być zagnieżdżony obiekt zawierający dane GraphQL
                    const urqlStateStr = JSON.stringify(topLevelPreview.props.pageProps.urqlState);
                    // Wyszukaj frazy związane z lokalizacją w całym obiekcie urqlState
                    const hasLocation = urqlStateStr.includes('"location"');
                    const hasAddress = urqlStateStr.includes('"address"');
                    const hasCity = urqlStateStr.includes('"city"');
                    const hasRegion = urqlStateStr.includes('"region"');
                    await this.logAction('info', `DEBUG: W urqlState znaleziono: location=${hasLocation}, address=${hasAddress}, city=${hasCity}, region=${hasRegion}`);
                  }
                  
                  // Pokaż klucze advert jeśli istnieje
                  if (topLevelPreview.props.pageProps.advert) {
                    const advertKeys = Object.keys(topLevelPreview.props.pageProps.advert);
                    await this.logAction('info', `DEBUG: Klucze w advert: ${advertKeys.join(', ')}`);
                  }
                }
              }
            } catch {
              // Ignorujemy błędy w tym kodzie debugowym
            }
            try {
              const nextData = JSON.parse(scriptContent);

              // Wyloguj dokładnie ścieżkę do advert, która jest najważniejsza
              const advertPath = nextData?.props?.pageProps?.advert;
              await this.logAction('info', `DEBUG: Istnienie props.pageProps.advert: ${!!advertPath}`);

              if (advertPath) {
                await this.logAction('info', `DEBUG: Klucze w props.pageProps.advert: ${Object.keys(advertPath).join(', ')}`);

                // Sprawdź czy istnieje seller
                if (advertPath.seller) {
                  await this.logAction('info', `DEBUG: Klucze w props.pageProps.advert.seller: ${Object.keys(advertPath.seller).join(', ')}`);
                }
              }

              // Sprawdź dokładną ścieżkę na podstawie przykładu
              let locationData = nextData?.props?.pageProps?.advert?.seller?.location;

              // Wyloguj szczegółowo dostępne dane lokalizacyjne, jeśli istnieją
              if (locationData) {
                await this.logAction('info', `DEBUG: Znaleziono dokładne dane lokalizacji: ${JSON.stringify(locationData)}`);
              } else {
                await this.logAction('info', `DEBUG: Nie znaleziono lokalizacji w ścieżce props.pageProps.advert.seller.location`);
              }

              // Sprawdź alternatywne ścieżki
              if (!locationData && nextData?.props?.pageProps?.advert?.location) {
                locationData = nextData.props.pageProps.advert.location;
                await this.logAction('info', `DEBUG: Znaleziono lokalizację w alternatywnej ścieżce props.pageProps.advert.location`);
              }

              // Nowa metoda: Przeszukaj urqlState w poszukiwaniu danych lokalizacyjnych
              if (!locationData && nextData?.props?.pageProps?.urqlState) {
                await this.logAction('info', `DEBUG: Przeszukuję urqlState w poszukiwaniu danych lokalizacyjnych...`);

                const urqlStateStr = JSON.stringify(nextData.props.pageProps.urqlState);

                // Szukaj wzorców lokalizacyjnych w urqlState
                const locationRegex = /"location":\s*{[^}]*"address":\s*"([^"]+)"[^}]*"city":\s*"([^"]+)"[^}]*"region":\s*"([^"]+)"/g;
                const locationMatch = locationRegex.exec(urqlStateStr);

                if (locationMatch) {
                  locationData = {
                    address: locationMatch[1],
                    city: locationMatch[2],
                    region: locationMatch[3]
                  };
                  await this.logAction('info', `DEBUG: Znaleziono lokalizację w urqlState: ${JSON.stringify(locationData)}`);
                } else {
                  // Alternatywny wzorzec - szukaj tylko regionu
                  const regionRegex = /"region":\s*"([^"]+)"/g;
                  const regionMatch = regionRegex.exec(urqlStateStr);

                  if (regionMatch) {
                    // Szukaj też miasta/adresu w pobliżu
                    const cityRegex = /"city":\s*"([^"]+)"/g;
                    const addressRegex = /"address":\s*"([^"]+)"/g;

                    const cityMatch = cityRegex.exec(urqlStateStr);
                    const addressMatch = addressRegex.exec(urqlStateStr);

                    locationData = {
                      region: regionMatch[1],
                      city: cityMatch ? cityMatch[1] : '',
                      address: addressMatch ? addressMatch[1] : ''
                    };
                    await this.logAction('info', `DEBUG: Znaleziono częściowe dane lokalizacji w urqlState: ${JSON.stringify(locationData)}`);
                  }
                }
              }

              // Jeszcze jedna metoda: Przeszukaj cały JSON za pomocą regex
              if (!locationData) {
                await this.logAction('info', `DEBUG: Przeszukuję cały JSON za pomocą regex...`);

                // Szukaj wzorca: "Warszawa, Śródmieście" i "Mazowieckie"
                const fullLocationRegex = /"address":\s*"([^"]+)",\s*"city":\s*"([^"]+)",\s*[^}]*"region":\s*"([^"]+)"/g;
                const fullMatch = fullLocationRegex.exec(scriptContent);

                if (fullMatch) {
                  locationData = {
                    address: fullMatch[1],
                    city: fullMatch[2],
                    region: fullMatch[3]
                  };
                  await this.logAction('info', `DEBUG: Znaleziono lokalizację przez regex w całym JSON: ${JSON.stringify(locationData)}`);
                }
              }

              // Ostateczny fallback: Szukaj wszystkich wystąpień "location": { w całym JSON
              if (!locationData) {
                await this.logAction('info', `DEBUG: Ostateczny fallback - szukam wszystkich wystąpień "location": { w JSON...`);

                // Znajdź wszystkie wystąpienia "location": { (proste bloki)
                const locationPattern = /"location":\s*\{([^}]+)\}/g;
                let match;

                while ((match = locationPattern.exec(scriptContent)) !== null) {
                  const locationContent = match[1];
                  await this.logAction('info', `DEBUG: Znaleziono blok location: {${locationContent}}`);

                  // Wyciągnij dane z tego bloku
                  const addressMatch = locationContent.match(/"address":\s*"([^"]+)"/);
                  const cityMatch = locationContent.match(/"city":\s*"([^"]+)"/);
                  const regionMatch = locationContent.match(/"region":\s*"([^"]+)"/);

                  if (regionMatch && (addressMatch || cityMatch)) {
                    locationData = {
                      address: addressMatch ? addressMatch[1] : '',
                      city: cityMatch ? cityMatch[1] : '',
                      region: regionMatch[1]
                    };
                    await this.logAction('info', `DEBUG: Znaleziono lokalizację w bloku location: ${JSON.stringify(locationData)}`);
                    break; // Użyj pierwszego znalezionego
                  } else if (addressMatch || cityMatch) {
                    // Jeśli mamy tylko adres/miasto bez województwa, zapisz to i spróbuj z Gemini
                    const locationText = addressMatch ? addressMatch[1] : cityMatch![1];
                    await this.logAction('info', `DEBUG: Znaleziono lokalizację bez województwa w bloku location: "${locationText}", używam Gemini API`);

                    const geminiRegion = await this.getRegionFromGemini(locationText);
                    if (geminiRegion) {
                      locationData = {
                        address: addressMatch ? addressMatch[1] : '',
                        city: cityMatch ? cityMatch[1] : '',
                        region: geminiRegion
                      };
                      await this.logAction('info', `DEBUG: Uzupełniono województwo przez Gemini: ${JSON.stringify(locationData)}`);
                      break;
                    }
                  }
                }

                // Jeśli nadal nie znaleziono, spróbuj z zagnieżdżonymi obiektami location
                if (!locationData) {
                  await this.logAction('info', `DEBUG: Szukam zagnieżdżonych obiektów location...`);

                  // Bardziej zaawansowany regex dla zagnieżdżonych obiektów
                  const nestedLocationPattern = /"location":\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g;
                  let nestedMatch;

                  while ((nestedMatch = nestedLocationPattern.exec(scriptContent)) !== null) {
                    const fullLocationBlock = nestedMatch[0];
                    await this.logAction('info', `DEBUG: Znaleziono zagnieżdżony blok location: ${fullLocationBlock.substring(0, 200)}...`);

                    // Wyciągnij dane z zagnieżdżonego bloku
                    const addressMatch = fullLocationBlock.match(/"address":\s*"([^"]+)"/);
                    const cityMatch = fullLocationBlock.match(/"city":\s*"([^"]+)"/);
                    const regionMatch = fullLocationBlock.match(/"region":\s*"([^"]+)"/);

                    if (regionMatch && (addressMatch || cityMatch)) {
                      locationData = {
                        address: addressMatch ? addressMatch[1] : '',
                        city: cityMatch ? cityMatch[1] : '',
                        region: regionMatch[1]
                      };
                      await this.logAction('info', `DEBUG: Znaleziono lokalizację w zagnieżdżonym bloku: ${JSON.stringify(locationData)}`);
                      break;
                    }
                  }
                }
              }
              
              if (locationData) {
                const { address, city, region } = locationData;

                // Sprawdź bezpośrednio region zamiast parsować go z tekstu
                if (region) {
                  const { allowed } = this.isAllowedRegion(undefined, region);

                  // Sformatuj lokalizację
                  let formattedLocation = '';
                  if (address && region) {
                    formattedLocation = `${address} (${region})`;
                  } else if (city && region) {
                    formattedLocation = `${city} (${region})`;
                  } else if (address) {
                    formattedLocation = address;
                  } else if (city) {
                    formattedLocation = city;
                  }

                  if (!allowed) {
                    // Jeżeli województwo nie jest dozwolone, ignoruj to ogłoszenie
                    await this.logAction('info', `Zignorowałem - złe województwo: ${formattedLocation} (region: ${region})`);
                    return { ignoredByRegion: true, location: formattedLocation };
                  } else {
                    await this.logAction('info', `Dozwolone województwo: ${region}, lokalizacja: ${formattedLocation}`);

                    // Pobierz dane z API
                    const apiUrl = `https://www.otomoto.pl/api/v1/ad/${adId}`;
                    await this.logAction('info', `Pobieranie danych API z: ${apiUrl}`);

                    const apiData = await this.fetchJson<Record<string, unknown>>(apiUrl);

                    // Dodaj lokalizację do danych API
                    if (formattedLocation) {
                      apiData.location = formattedLocation;
                    }

                    return apiData;
                  }
                } else if (address || city) {
                  // Jeśli mamy miasto/adres ale nie mamy województwa, użyj Gemini API
                  const locationText = address || city;
                  await this.logAction('info', `DEBUG: Znaleziono lokalizację bez województwa: "${locationText}", używam Gemini API`);

                  const geminiRegion = await this.getRegionFromGemini(locationText);
                  if (geminiRegion) {
                    const { allowed } = this.isAllowedRegion(undefined, geminiRegion);
                    const formattedLocation = `${locationText} (${geminiRegion})`;

                    if (!allowed) {
                      await this.logAction('info', `Zignorowałem - złe województwo z Gemini: ${formattedLocation} (region: ${geminiRegion})`);
                      return { ignoredByRegion: true, location: formattedLocation };
                    } else {
                      await this.logAction('info', `Dozwolone województwo z Gemini: ${geminiRegion}, lokalizacja: ${formattedLocation}`);

                      // Pobierz dane z API
                      const apiUrl = `https://www.otomoto.pl/api/v1/ad/${adId}`;
                      await this.logAction('info', `Pobieranie danych API z: ${apiUrl}`);

                      const apiData = await this.fetchJson<Record<string, unknown>>(apiUrl);

                      // Dodaj lokalizację do danych API
                      apiData.location = formattedLocation;

                      return apiData;
                    }
                  }
                }
              }
            } catch (parseError) {
              await this.logAction('error', `Błąd parsowania JSON z __NEXT_DATA__ dla ogłoszenia ${adId}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
            }
          }
        }
        
        // Fallback do starej metody, jeśli nie udało się wyciągnąć regionu z JSON
        const location = await this.extractLocationFromAdHtml(response.data, adId);

        if (location) {
          // Sprawdź czy województwo jest na liście dozwolonych
          const { allowed, region } = this.isAllowedRegion(location);

          if (!allowed && !region) {
            // Jeśli nie znaleziono województwa, spróbuj z Gemini API
            await this.logAction('info', `Nie znaleziono województwa w lokalizacji "${location}", próbuję z Gemini API`);
            const geminiRegion = await this.getRegionFromGemini(location);

            if (geminiRegion) {
              const { allowed: geminiAllowed } = this.isAllowedRegion(undefined, geminiRegion);
              const formattedLocation = `${location} (${geminiRegion})`;

              if (!geminiAllowed) {
                await this.logAction('info', `Zignorowałem - złe województwo z Gemini: ${formattedLocation} (region: ${geminiRegion})`);
                return { ignoredByRegion: true, location: formattedLocation };
              } else {
                await this.logAction('info', `Dozwolone województwo z Gemini: ${geminiRegion}, lokalizacja: ${formattedLocation}`);

                // Pobierz dane z API
                const apiUrl = `https://www.otomoto.pl/api/v1/ad/${adId}`;
                await this.logAction('info', `Pobieranie danych API z: ${apiUrl}`);

                const apiData = await this.fetchJson<Record<string, unknown>>(apiUrl);
                apiData.location = formattedLocation;

                return apiData;
              }
            } else {
              await this.logAction('info', `Gemini API nie rozpoznał województwa dla "${location}"`);
            }
          } else if (!allowed) {
            // Jeżeli województwo nie jest dozwolone, ignoruj to ogłoszenie
            await this.logAction('info', `Zignorowałem - złe województwo: ${location} (region: ${region || 'nie wykryto'})`);
            return { ignoredByRegion: true, location };
          } else {
            await this.logAction('info', `Dozwolone województwo: ${region}, lokalizacja: ${location}`);
          }
        }
        
        // Pobierz dane z API
        const apiUrl = `https://www.otomoto.pl/api/v1/ad/${adId}`;
        await this.logAction('info', `Pobieranie danych API z: ${apiUrl}`);
        
        const apiData = await this.fetchJson<Record<string, unknown>>(apiUrl);
        
        // Dodaj lokalizację do danych API
        if (location) {
          apiData.location = location;
        }
        
        return apiData;
      } catch (error) {
        // Jeśli nie udało się pobrać lokalizacji, kontynuuj tylko z danymi z API
        await this.logAction('error', `Błąd podczas pobierania strony ogłoszenia: ${error instanceof Error ? error.message : String(error)}`);
        
        // Pobierz dane z API mimo błędu z lokalizacją
        const apiUrl = `https://www.otomoto.pl/api/v1/ad/${adId}`;
        await this.logAction('info', `Pobieranie tylko danych API z: ${apiUrl}`);
        return await this.fetchJson(apiUrl);
      }
    } catch (error) {
      await this.logAction('error', `Błąd podczas pobierania szczegółów dla ogłoszenia ${adId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  // Pobranie numerów telefonów dla ogłoszenia
  async fetchAdPhones(adId: string): Promise<string[]> {
    try {
      const phonesUrl = `https://www.otomoto.pl/ajax/misc/contact/all_phones/${adId}`;
      await this.logAction('info', `Pobieranie numerów telefonów dla ogłoszenia ${adId}`);
      
      const phonesData = await this.fetchJson<Array<{ number: string }>>(phonesUrl);
      
      if (Array.isArray(phonesData)) {
        const phones = phonesData.map(phone => phone.number);
        await this.logAction('info', `Pobrano ${phones.length} numerów telefonów dla ogłoszenia ${adId}`);
        return phones;
      }
      
      return [];
    } catch (error) {
      await this.logAction('error', `Błąd podczas pobierania numerów telefonów dla ogłoszenia ${adId}: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  }

  // Określenie liczby stron do przescrapowania
  async getNumberOfPages(url: string): Promise<number> {
    try {
      // Dodaj kilka prób w przypadku błędu 403
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          const $ = await this.fetchWithCheerio(url);
          
          // Strategie określania liczby stron
          
          // Nowa strategia: Znajdź przedostatni element li w ul[data-sentry-element='Pagination']
          const paginationUl = $('ul[data-sentry-element="Pagination"]');
          if (paginationUl.length) {
            const liElements = paginationUl.find('li');
            if (liElements.length >= 2) {
              const secondLastLi = liElements.eq(liElements.length - 2);
              const pageNumber = parseInt(secondLastLi.text().trim());
              if (!isNaN(pageNumber)) {
                await this.logAction('info', `Znaleziono liczbę stron (nowa metoda): ${pageNumber}`);
                return pageNumber;
              }
            }
          }
          
          // Strategia 1: Znajdź element przed przyciskiem "Go to next Page"
          const prevElement = $('li[title="Go to next Page"]').prev();
          if (prevElement.length && prevElement.text().trim()) {
            const pageNumber = parseInt(prevElement.text().trim());
            if (!isNaN(pageNumber)) {
              await this.logAction('info', `Znaleziono liczbę stron (metoda 1): ${pageNumber}`);
              return pageNumber;
            }
          }
          
          // Strategia 2: Znajdź ostatni element numeracji
          const paginationItems = $('[data-testid="pagination-list"] li');
          if (paginationItems.length > 0) {
            let lastNumber = 1;
            
            paginationItems.each((_, item) => {
              const text = $(item).text().trim();
              const num = parseInt(text);
              if (!isNaN(num) && num > lastNumber) {
                lastNumber = num;
              }
            });
            
            if (lastNumber > 1) {
              await this.logAction('info', `Znaleziono liczbę stron (metoda 2): ${lastNumber}`);
              return lastNumber;
            }
          }
          
          // Strategia 3: Sprawdź obecność przycisku Next
          if ($('[data-testid="pagination-step-forwards"], li[title="Go to next Page"]').length) {
            await this.logAction('info', `Znaleziono przycisk "następna strona" - zwracam ${CheerioHelper.FALLBACK_PAGES_COUNT} (fallback)`);
            return CheerioHelper.FALLBACK_PAGES_COUNT;
          }

          // Domyślnie: jedna strona
          await this.logAction('info', `Nie znaleziono informacji o liczbie stron - zwracam 1`);
          return 1;
        } catch (attemptError) {
          // Sprawdź, czy błąd jest typu AxiosError i ma kod 403
          if (axios.isAxiosError(attemptError) && attemptError.response?.status === 403) {
            await this.logAction('warn', `Otrzymano błąd 403 przy próbie ${attempt}/3. Ponowna próba za ${2000 * attempt}ms`);
            await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
            continue;
          }
          throw attemptError;
        }
      }
      return CheerioHelper.FALLBACK_PAGES_COUNT; // Fallback, jeśli wszystkie próby zawiodą
    } catch (error) {
      await this.logAction('error', `Błąd podczas określania liczby stron: ${error instanceof Error ? error.message : String(error)}`);
      return CheerioHelper.FALLBACK_PAGES_COUNT; // Fallback w przypadku błędu
    }
  }

  // Funkcja do usuwania wszystkich ogłoszeń z bazy danych
  public async clearAllAds(): Promise<number> {
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();
      
      const result = await prisma.ad.deleteMany({});
      await prisma.$disconnect();
      
      await this.logAction('info', `Usunięto wszystkie ogłoszenia z bazy danych (${result.count})`);
      return result.count;
    } catch (error) {
      await this.logAction('error', `Błąd podczas usuwania ogłoszeń: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  // Funkcja do usuwania wszystkich numerów telefonów z bazy danych
  public async clearAllPhones(): Promise<number> {
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      // Usuń numery telefonów powiązane z ogłoszeniami
      const phoneResult = await prisma.phone.deleteMany({});

      // Usuń numery telefonów do wysyłki
      const phoneToSendResult = await prisma.phoneToSend.deleteMany({});

      await prisma.$disconnect();

      const totalCount = phoneResult.count + phoneToSendResult.count;

      await this.logAction('info', `Usunięto wszystkie numery telefonów z bazy danych (${phoneResult.count} z ogłoszeń + ${phoneToSendResult.count} do wysyłki = ${totalCount} łącznie)`);
      return totalCount;
    } catch (error) {
      await this.logAction('error', `Błąd podczas usuwania numerów telefonów: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}
